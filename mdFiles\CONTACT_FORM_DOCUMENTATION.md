# Contact Form System Documentation

## Overview

The Contact Form system provides a comprehensive solution for managing customer inquiries and feedback in L Café. It includes a public contact form for customers to submit messages and a complete admin interface for managing, tracking, and responding to these messages.

## Architecture

### Database Model

#### Contact Schema (`backend/models/contact.model.js`)

```javascript
const contactSchema = new mongoose.Schema({
  name: { 
    type: String, 
    required: true,
    trim: true,
    maxlength: 100
  },
  email: { 
    type: String, 
    required: true,
    lowercase: true,
    trim: true,
    maxlength: 255
  },
  message: { 
    type: String, 
    required: true,
    trim: true,
    maxlength: 2000
  },
  status: {
    type: String,
    enum: ['new', 'read', 'replied', 'archived'],
    default: 'new'
  },
  ipAddress: { type: String },
  userAgent: { type: String },
  replied: { type: Boolean, default: false },
  repliedAt: { type: Date },
  repliedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  notes: { type: String, maxlength: 1000 },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});
```

**Key Features:**
- **Status Tracking**: Four-stage workflow (new → read → replied → archived)
- **User Association**: Links replies to admin users
- **Metadata Collection**: IP address and user agent for analytics
- **Notes System**: Internal notes for admin use
- **Validation**: Input length limits and email validation

### Backend Implementation

#### API Endpoints (`backend/routes/contact.routes.js`)

**Public Routes:**
- `POST /api/contact/submit` - Submit contact form (CSRF protected)

**Admin Routes (Authentication Required):**
- `GET /api/contact` - Get all contact messages with pagination
- `GET /api/contact/stats` - Get contact statistics
- `GET /api/contact/:id` - Get single contact message
- `PUT /api/contact/:id/status` - Update message status
- `DELETE /api/contact/:id` - Delete contact message

#### Controller Functions (`backend/controllers/contact.controller.js`)

**Public Submission:**
```javascript
export const submitContactForm = async (req, res) => {
  try {
    const { name, email, message } = req.body;

    // Comprehensive validation
    if (!name || !email || !message) {
      return res.status(400).json({ 
        message: 'Name, email, and message are required' 
      });
    }

    // Create contact message with metadata
    const contactMessage = new Contact({
      name: name.trim(),
      email: email.trim().toLowerCase(),
      message: message.trim(),
      status: 'new',
      ipAddress: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent')
    });

    await contactMessage.save();

    res.status(201).json({
      message: 'Thank you for your message! We\'ll get back to you soon.',
      id: contactMessage._id
    });
  } catch (error) {
    res.status(500).json({ 
      message: 'Error submitting contact form', 
      error: error.message 
    });
  }
};
```

**Admin Management:**
- **Pagination Support**: Efficient handling of large message volumes
- **Search Functionality**: Search across name, email, and message content
- **Status Filtering**: Filter by message status
- **Statistics**: Real-time analytics and metrics

### Frontend Implementation

#### Contact Form Component (`src/components/ContactForm.tsx`)

**Features:**
- **Form Validation**: Zod schema validation with real-time feedback
- **CSRF Protection**: Automatic CSRF token handling
- **Loading States**: Visual feedback during submission
- **Error Handling**: Comprehensive error display
- **Success Feedback**: Toast notifications for successful submissions

**Validation Schema:**
```typescript
const formSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  message: z.string().min(10, "Message must be at least 10 characters"),
});
```

#### Admin Dashboard (`src/components/dashboard/DashboardContacts.tsx`)

**Management Features:**
- **Message List**: Paginated view of all contact messages
- **Status Management**: Quick status updates with dropdown
- **Search and Filter**: Real-time search and status filtering
- **Message Details**: Modal view for complete message information
- **Statistics Dashboard**: Key metrics and analytics
- **Bulk Operations**: Efficient management of multiple messages

**Status Workflow:**
1. **New**: Freshly submitted messages
2. **Read**: Messages that have been viewed by admin
3. **Replied**: Messages that have received a response
4. **Archived**: Completed or closed messages

## Security Features

### Input Validation
- **Server-Side Validation**: Comprehensive validation on all inputs
- **Length Limits**: Prevents oversized submissions
- **Email Validation**: Regex-based email format checking
- **XSS Prevention**: Input sanitization and escaping

### CSRF Protection
- **Token Validation**: All form submissions require valid CSRF tokens
- **Automatic Handling**: Frontend automatically manages tokens
- **Error Recovery**: Graceful handling of token failures

### Rate Limiting
- **IP-Based Tracking**: Monitors submission frequency per IP
- **Spam Prevention**: Prevents automated form submissions
- **User Agent Logging**: Tracks submission sources

## API Reference

### Submit Contact Form

```http
POST /api/contact/submit
Content-Type: application/json
X-CSRF-Token: <csrf-token>

{
  "name": "John Doe",
  "email": "<EMAIL>",
  "message": "I love your coffee! When are you open on Sundays?"
}
```

**Response:**
```json
{
  "message": "Thank you for your message! We'll get back to you soon.",
  "id": "60f7b3b3b3b3b3b3b3b3b3b3"
}
```

### Get Contact Messages (Admin)

```http
GET /api/contact?page=1&limit=10&status=new&search=coffee
Authorization: Bearer <admin-token>
```

**Response:**
```json
{
  "messages": [
    {
      "_id": "60f7b3b3b3b3b3b3b3b3b3b3",
      "name": "John Doe",
      "email": "<EMAIL>",
      "message": "I love your coffee!",
      "status": "new",
      "ipAddress": "***********",
      "replied": false,
      "createdAt": "2023-07-21T10:30:00.000Z",
      "updatedAt": "2023-07-21T10:30:00.000Z"
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 5,
    "total": 50,
    "hasNext": true,
    "hasPrev": false
  }
}
```

### Update Message Status (Admin)

```http
PUT /api/contact/:id/status
Authorization: Bearer <admin-token>
X-CSRF-Token: <csrf-token>
Content-Type: application/json

{
  "status": "replied",
  "notes": "Responded via email on 2023-07-21"
}
```

## Usage Guide

### For Customers

1. **Access Contact Form**:
   - Navigate to `/contact` page
   - Fill in name, email, and message
   - Submit form

2. **Form Validation**:
   - Name: Minimum 2 characters
   - Email: Valid email format required
   - Message: Minimum 10 characters

3. **Submission Feedback**:
   - Success: Green toast notification
   - Error: Red toast with specific error message

### For Administrators

1. **Access Contact Dashboard**:
   - Login as admin user
   - Navigate to Dashboard → Contact Messages

2. **View Messages**:
   - Browse paginated message list
   - Use search to find specific messages
   - Filter by status (new, read, replied, archived)

3. **Manage Messages**:
   - Click eye icon to view full message details
   - Use status dropdown to update message status
   - Add internal notes for tracking
   - Delete messages when necessary

4. **Monitor Statistics**:
   - View total message count
   - Track today's submissions
   - Monitor new vs. replied ratios
   - Analyze message trends

## Performance Considerations

### Database Optimization
- **Indexes**: Optimized queries with proper indexing
- **Pagination**: Efficient handling of large datasets
- **Aggregation**: Statistics calculated via MongoDB aggregation

### Frontend Performance
- **React Query**: Caching and background updates
- **Lazy Loading**: Components loaded on demand
- **Debounced Search**: Prevents excessive API calls
- **Optimistic Updates**: Immediate UI feedback

## Monitoring and Analytics

### Message Tracking
- **Submission Metrics**: Track daily/weekly submission volumes
- **Response Times**: Monitor time from submission to reply
- **Status Distribution**: Analyze message workflow efficiency
- **Source Analysis**: IP and user agent tracking

### Admin Activity
- **User Tracking**: Monitor which admins handle messages
- **Response Patterns**: Analyze admin response efficiency
- **Status Changes**: Track message lifecycle progression

## Troubleshooting

### Common Issues

#### Form Submission Failures
- **CSRF Token Issues**: Ensure tokens are properly generated and included
- **Validation Errors**: Check input format and length requirements
- **Network Issues**: Verify API connectivity and CORS settings

#### Admin Dashboard Problems
- **Authentication**: Ensure admin user is properly logged in
- **Permission Issues**: Verify admin role assignment
- **Loading Issues**: Check API endpoints and data formatting

### Debug Commands

```bash
# Test contact form submission
curl -X POST http://localhost:5000/api/contact/submit \
  -H "Content-Type: application/json" \
  -H "X-CSRF-Token: YOUR_TOKEN" \
  -d '{"name":"Test","email":"<EMAIL>","message":"Test message"}'

# Check contact messages in database
mongo l-cafe --eval "db.contacts.find().pretty()"

# View contact statistics
curl -H "Authorization: Bearer ADMIN_TOKEN" \
  http://localhost:5000/api/contact/stats
```

## Future Enhancements

### Planned Features
- **Email Notifications**: Automatic email alerts for new messages
- **Response Templates**: Pre-defined response templates
- **Auto-Reply**: Automatic acknowledgment emails
- **Message Categories**: Categorize messages by topic
- **Priority Levels**: Mark urgent messages
- **Export Functionality**: Export messages to CSV/PDF

### Integration Possibilities
- **CRM Integration**: Connect with customer relationship management systems
- **Help Desk**: Integration with support ticket systems
- **Analytics**: Advanced reporting and analytics dashboard
- **Mobile App**: Mobile admin interface for message management

## Conclusion

The Contact Form system provides a complete solution for customer communication management in L Café. It combines user-friendly public interfaces with powerful admin tools, ensuring efficient handling of customer inquiries while maintaining security and performance standards.

Key strengths:
- **Complete Workflow**: From submission to resolution tracking
- **Security-First**: CSRF protection and input validation
- **Admin-Friendly**: Intuitive management interface
- **Scalable**: Efficient handling of high message volumes
- **Analytics-Ready**: Comprehensive tracking and reporting
