# Announcement System Documentation

## Overview

The Announcement System is a comprehensive feature of the L Café application that allows administrators to create, manage, and display announcements to users. The system supports categorization, featured announcements, and automatic sitemap integration.

## Architecture

### Backend Components

#### 1. Database Model (`backend/models/announcement.model.js`)

The announcement model defines the structure for storing announcements in MongoDB:

```javascript
const announcementSchema = new mongoose.Schema({
  title: { type: String, required: true },
  slug: { type: String, required: true, unique: true },
  content: { type: String, required: true },
  summary: { type: String },
  category: { 
    type: String, 
    enum: ['event', 'menu', 'general', 'promotion'],
    default: 'general'
  },
  image: { type: String },
  featured: { type: Boolean, default: false },
  publishDate: { type: Date, default: Date.now },
  expiryDate: { type: Date },
  author: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  active: { type: Boolean, default: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});
```

**Key Features:**
- **Slug Generation**: Unique URL-friendly identifiers for SEO
- **Categorization**: Four categories (event, menu, general, promotion)
- **Featured System**: Ability to mark announcements as featured
- **Soft Delete**: Uses `active` field for soft deletion
- **Author Tracking**: Links to user who created the announcement
- **Timestamps**: Automatic creation and update tracking

#### 2. Controller (`backend/controllers/announcement.controller.js`)

The controller handles all business logic for announcement operations:

**Core Functions:**

1. **`createUniqueSlug(title)`**
   - Generates URL-friendly slugs using the `slugify` library
   - Ensures uniqueness by appending numbers if conflicts exist
   - Supports Turkish characters (`locale: 'tr'`)

2. **`getAnnouncements(req, res)`**
   - Retrieves announcements with pagination and filtering
   - Supports query parameters: `page`, `limit`, `category`, `featured`, `active`
   - Returns paginated results with metadata

3. **`getAnnouncementBySlug(req, res)`**
   - Fetches a single announcement by its slug
   - Only returns active announcements
   - Populates author information

4. **`createAnnouncement(req, res)`**
   - Creates new announcements with validation
   - Automatically generates unique slugs
   - Integrates with sitemap auto-update system
   - Requires authentication

5. **`updateAnnouncement(req, res)`**
   - Updates existing announcements
   - Regenerates slug if title changes
   - Updates sitemap automatically
   - Requires authentication and CSRF protection

6. **`deleteAnnouncement(req, res)`**
   - Performs soft delete (sets `active: false`)
   - Updates sitemap automatically
   - Requires authentication and CSRF protection

7. **`getFeaturedAnnouncements(req, res)`**
   - Returns up to 5 featured announcements
   - Sorted by publish date (newest first)
   - Public endpoint (no authentication required)

8. **`hardDeleteAnnouncement(req, res)`**
   - Permanently removes announcements from database
   - Admin-only operation
   - Updates sitemap automatically

#### 3. Routes (`backend/routes/announcement.routes.js`)

The routing system organizes endpoints by access level:

**Public Routes (No Authentication):**
- `GET /api/announcements` - List announcements with pagination
- `GET /api/announcements/featured` - Get featured announcements
- `GET /api/announcements/:slug` - Get single announcement by slug

**Protected Routes (Authentication + CSRF):**
- `POST /api/announcements` - Create new announcement
- `PUT /api/announcements/:id` - Update announcement
- `DELETE /api/announcements/:id` - Soft delete announcement

**Admin-Only Routes:**
- `DELETE /api/announcements/:id/permanent` - Hard delete announcement

#### 4. Middleware Integration

**Security Middleware:**
- **Authentication**: `authMiddleware` validates JWT tokens
- **CSRF Protection**: `csrfProtection` prevents cross-site request forgery
- **Role-Based Access**: `roleMiddleware(['admin'])` for admin-only operations

**Automatic Features:**
- **Sitemap Integration**: Automatically updates sitemap on CRUD operations
- **Author Assignment**: Automatically assigns authenticated user as author

### Frontend Components

#### 1. Service Layer (`src/services/announcement.service.ts`)

The service layer handles API communication:

```typescript
interface Announcement {
  _id: string;
  title: string;
  content: string;
  summary?: string;
  category: string;
  slug: string;
  image?: string;
  author: {
    _id: string;
    username: string;
    firstName: string;
    lastName: string;
  };
  publishDate: string;
  expiryDate?: string;
  featured: boolean;
}
```

**Service Methods:**
- `getAnnouncements(page, limit)` - Fetch paginated announcements
- `getAnnouncementBySlug(slug)` - Fetch single announcement
- `getFeaturedAnnouncements()` - Fetch featured announcements
- `createAnnouncement(data)` - Create new announcement
- `updateAnnouncement(id, data)` - Update existing announcement
- `deleteAnnouncement(id)` - Delete announcement

**CSRF Token Handling:**
All write operations automatically ensure CSRF tokens are available before making requests.

#### 2. Dashboard Management (`src/components/dashboard/DashboardAnnouncements.tsx`)

The admin dashboard provides full CRUD functionality:

**Features:**
- **Table View**: Displays announcements in a sortable table
- **Pagination**: Handles large datasets efficiently
- **Create Dialog**: Form for creating new announcements
- **Edit Dialog**: Form for updating existing announcements
- **Delete Confirmation**: Safety dialog for deletion operations
- **Real-time Updates**: Uses React Query for cache invalidation

**Form Fields:**
- Title (required)
- Content (required)
- Summary (optional)
- Category (dropdown: event, menu, general, promotion)
- Featured (checkbox)

#### 3. Public Display Components

**Featured Announcements (`src/components/FeaturedAnnouncements.tsx`):**
- Displays up to 3 featured announcements on homepage
- Card-based layout with title, date, and summary
- Responsive grid design

**Announcements Page (`src/pages/Announcements.tsx`):**
- Public listing of all active announcements
- Pagination support
- Category badges with color coding
- Card-based responsive layout

**About Page Integration (`src/pages/About.tsx`):**
- Shows featured announcements in about section
- Compact card format

## API Endpoints

### Public Endpoints

#### GET /api/announcements
Retrieve announcements with optional filtering and pagination.

**Query Parameters:**
- `page` (number, default: 1) - Page number
- `limit` (number, default: 10) - Items per page
- `category` (string) - Filter by category
- `featured` (boolean) - Filter featured announcements
- `active` (boolean, default: true) - Filter active announcements

**Response:**
```json
{
  "announcements": [...],
  "totalPages": 5,
  "currentPage": 1,
  "total": 47
}
```

#### GET /api/announcements/featured
Retrieve featured announcements (max 5).

**Response:**
```json
[
  {
    "_id": "...",
    "title": "Special Event",
    "slug": "special-event",
    "content": "...",
    "category": "event",
    "featured": true,
    "publishDate": "2024-01-15T10:00:00.000Z",
    "author": {
      "firstName": "John",
      "lastName": "Doe"
    }
  }
]
```

#### GET /api/announcements/:slug
Retrieve a single announcement by slug.

**Response:**
```json
{
  "_id": "...",
  "title": "Announcement Title",
  "slug": "announcement-title",
  "content": "Full announcement content...",
  "summary": "Brief summary",
  "category": "general",
  "featured": false,
  "publishDate": "2024-01-15T10:00:00.000Z",
  "author": {
    "_id": "...",
    "username": "admin",
    "firstName": "John",
    "lastName": "Doe"
  }
}
```

### Protected Endpoints

#### POST /api/announcements
Create a new announcement.

**Headers:**
- `Authorization: Bearer <token>`
- `X-CSRF-Token: <csrf_token>`

**Request Body:**
```json
{
  "title": "New Announcement",
  "content": "Announcement content...",
  "summary": "Brief summary",
  "category": "general",
  "featured": false,
  "image": "optional-image-url"
}
```

**Response:**
```json
{
  "message": "Announcement created successfully",
  "announcement": { ... }
}
```

#### PUT /api/announcements/:id
Update an existing announcement.

**Headers:**
- `Authorization: Bearer <token>`
- `X-CSRF-Token: <csrf_token>`

**Request Body:** (partial update supported)
```json
{
  "title": "Updated Title",
  "content": "Updated content...",
  "featured": true
}
```

#### DELETE /api/announcements/:id
Soft delete an announcement.

**Headers:**
- `Authorization: Bearer <token>`
- `X-CSRF-Token: <csrf_token>`

**Response:**
```json
{
  "message": "Announcement deleted successfully"
}
```

### Admin-Only Endpoints

#### DELETE /api/announcements/:id/permanent
Permanently delete an announcement (hard delete).

**Headers:**
- `Authorization: Bearer <token>`
- `X-CSRF-Token: <csrf_token>`

**Response:**
```json
{
  "message": "Announcement permanently deleted"
}
```

## Security Features

### Authentication & Authorization
- **JWT Authentication**: All write operations require valid JWT tokens
- **Role-Based Access**: Admin-only operations are protected
- **CSRF Protection**: All state-changing operations require CSRF tokens

### Data Validation
- **Input Validation**: Required fields are validated on both frontend and backend
- **MongoDB ObjectId Validation**: Ensures valid IDs for operations
- **Slug Uniqueness**: Automatic handling of duplicate slugs

### Soft Delete Pattern
- Announcements are never permanently deleted by default
- Uses `active: false` for soft deletion
- Admin users can perform hard deletes when necessary

## Integration Features

### Sitemap Integration
The announcement system automatically integrates with the sitemap system:

- **Create**: Adds new announcement URLs to sitemap
- **Update**: Updates existing entries if slug changes
- **Delete**: Removes URLs from sitemap

### Author Tracking
- Automatically assigns the authenticated user as the author
- Populates author information in responses
- Maintains audit trail for content creation

## Error Handling

### Backend Error Responses
All endpoints return consistent error responses:

```json
{
  "message": "Error description",
  "error": "Detailed error message (development only)"
}
```

**Common HTTP Status Codes:**
- `200` - Success
- `201` - Created
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (insufficient permissions or CSRF error)
- `404` - Not Found
- `500` - Internal Server Error

### Frontend Error Handling
- **Toast Notifications**: User-friendly error messages
- **Loading States**: Visual feedback during operations
- **Retry Logic**: Automatic CSRF token refresh on failures

## Performance Considerations

### Database Optimization
- **Indexing**: Unique index on slug field for fast lookups
- **Pagination**: Efficient skip/limit queries for large datasets
- **Population**: Selective field population for author data

### Frontend Optimization
- **React Query**: Intelligent caching and background updates
- **Lazy Loading**: Components load only when needed
- **Debounced Searches**: Prevents excessive API calls

## Deployment Notes

### Environment Variables
No specific environment variables required for announcements.

### Database Migrations
The announcement collection is created automatically when the first announcement is saved.

### Dependencies
**Backend:**
- `mongoose` - MongoDB ODM
- `slugify` - URL-friendly slug generation
- `express` - Web framework
- `jsonwebtoken` - JWT authentication
- `csurf` - CSRF protection

**Frontend:**
- `@tanstack/react-query` - Data fetching and caching
- `react-hook-form` - Form management
- `date-fns` - Date formatting

## Testing Recommendations

### Backend Testing
1. **Unit Tests**: Test individual controller functions
2. **Integration Tests**: Test complete API endpoints
3. **Authentication Tests**: Verify security middleware
4. **Database Tests**: Test model validations and constraints

### Frontend Testing
1. **Component Tests**: Test individual React components
2. **Service Tests**: Test API service functions
3. **Integration Tests**: Test complete user workflows
4. **Accessibility Tests**: Ensure WCAG compliance

## Troubleshooting

### Common Issues

1. **CSRF Token Errors**
   - Ensure frontend is properly fetching and sending CSRF tokens
   - Check cookie settings in CSRF middleware

2. **Slug Conflicts**
   - The system automatically handles conflicts by appending numbers
   - Check `createUniqueSlug` function for custom logic

3. **Authentication Failures**
   - Verify JWT token is valid and not expired
   - Check Authorization header format: `Bearer <token>`

4. **Pagination Issues**
   - Ensure page and limit parameters are positive integers
   - Check total count calculations in controller

### Debugging Tips

1. **Enable Debug Logging**: Check console logs for detailed error information
2. **Database Queries**: Use MongoDB Compass to inspect data directly
3. **Network Tab**: Monitor API requests and responses in browser dev tools
4. **React Query DevTools**: Use for debugging cache and query states

## Code Examples

### Backend Code Snippets

#### Creating a Unique Slug
```javascript
const createUniqueSlug = async (title) => {
  let slug = slugify(title, {
    lower: true,
    strict: true,
    locale: 'tr'
  });

  let exists = await Announcement.findOne({ slug });

  if (exists) {
    let count = 1;
    let newSlug = slug;

    while (exists) {
      newSlug = `${slug}-${count}`;
      exists = await Announcement.findOne({ slug: newSlug });
      count++;
    }

    slug = newSlug;
  }

  return slug;
};
```

#### Announcement Controller Example
```javascript
export const createAnnouncement = async (req, res) => {
  try {
    const { title, content, summary, category, image, featured, publishDate, expiryDate } = req.body;

    if (!title || !content) {
      return res.status(400).json({ message: 'Title and content are required' });
    }

    const slug = await createUniqueSlug(title);

    const announcement = new Announcement({
      title,
      slug,
      content,
      summary,
      category,
      image,
      featured,
      publishDate: publishDate || Date.now(),
      expiryDate,
      author: req.user.id,
      active: true
    });

    await announcement.save();

    await autoUpdateSitemap('announcement', 'create', {
      id: announcement._id,
      title: announcement.title,
      slug: announcement.slug
    });

    res.status(201).json({
      message: 'Announcement created successfully',
      announcement
    });
  } catch (error) {
    res.status(500).json({
      message: 'Error creating announcement',
      error: error.message
    });
  }
};
```

### Frontend Code Snippets

#### Service Layer Implementation
```typescript
const announcementService = {
  getAnnouncements: async (page = 1, limit = 10) => {
    return api.get(`/announcements?page=${page}&limit=${limit}`);
  },

  createAnnouncement: async (data: Omit<Announcement, '_id' | 'author' | 'slug'>) => {
    try {
      await authService.getCsrfToken();
      return await api.post('/announcements', data);
    } catch (error) {
      console.error('Error in createAnnouncement service:', error);
      throw error;
    }
  },

  updateAnnouncement: async (id: string, data: Partial<Announcement>) => {
    try {
      await authService.getCsrfToken();
      return await api.put(`/announcements/${id}`, data);
    } catch (error) {
      console.error('Error in updateAnnouncement service:', error);
      throw error;
    }
  }
};
```

#### React Component Example
```typescript
const DashboardAnnouncements = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data, isLoading } = useQuery({
    queryKey: ['announcements', currentPage],
    queryFn: async () => {
      const response = await announcementService.getAnnouncements(currentPage, 10);
      return response.data;
    },
  });

  const createMutation = useMutation({
    mutationFn: async (data: any) => {
      return await announcementService.createAnnouncement(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['announcements'] });
      toast({
        title: "Success",
        description: "Announcement created successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to create announcement",
        variant: "destructive",
      });
    },
  });

  return (
    <div>
      {/* Component JSX */}
    </div>
  );
};
```

## Database Schema Details

### Announcement Collection Structure
```javascript
{
  _id: ObjectId("..."),
  title: "Sample Announcement",
  slug: "sample-announcement",
  content: "This is the full content of the announcement...",
  summary: "Brief summary for preview",
  category: "general", // enum: ['event', 'menu', 'general', 'promotion']
  image: "https://example.com/image.jpg", // optional
  featured: false,
  publishDate: ISODate("2024-01-15T10:00:00.000Z"),
  expiryDate: ISODate("2024-02-15T10:00:00.000Z"), // optional
  author: ObjectId("..."), // references User collection
  active: true,
  createdAt: ISODate("2024-01-15T10:00:00.000Z"),
  updatedAt: ISODate("2024-01-15T10:00:00.000Z")
}
```

### Indexes
```javascript
// Unique index on slug for fast lookups and uniqueness
db.announcements.createIndex({ "slug": 1 }, { unique: true })

// Compound index for efficient filtering and sorting
db.announcements.createIndex({ "active": 1, "featured": 1, "publishDate": -1 })

// Index on category for category-based filtering
db.announcements.createIndex({ "category": 1 })

// Index on author for author-based queries
db.announcements.createIndex({ "author": 1 })
```

## File Structure

### Backend Files
```
backend/
├── models/
│   └── announcement.model.js          # MongoDB schema definition
├── controllers/
│   └── announcement.controller.js     # Business logic and API handlers
├── routes/
│   └── announcement.routes.js         # Route definitions and middleware
└── middleware/
    ├── auth.middleware.js             # JWT authentication
    ├── csrf.middleware.js             # CSRF protection
    └── role.middleware.js             # Role-based access control
```

### Frontend Files
```
src/
├── services/
│   └── announcement.service.ts        # API service layer
├── components/
│   ├── FeaturedAnnouncements.tsx      # Homepage featured display
│   └── dashboard/
│       └── DashboardAnnouncements.tsx # Admin management interface
└── pages/
    ├── Announcements.tsx              # Public announcements page
    └── About.tsx                      # About page with announcements
```

## Future Enhancements

### Potential Features
1. **Rich Text Editor**: WYSIWYG editor for announcement content
2. **Image Upload**: Direct image upload functionality with file management
3. **Scheduling**: Ability to schedule announcements for future publication
4. **Email Notifications**: Notify subscribers of new announcements
5. **Comments System**: Allow user comments and discussions on announcements
6. **Analytics**: Track announcement views, engagement, and performance metrics
7. **Multi-language Support**: Internationalization for global audiences
8. **Approval Workflow**: Multi-step approval process for content moderation
9. **Tags System**: Flexible tagging system beyond categories
10. **Search Functionality**: Full-text search across announcement content

### Technical Improvements
1. **Full-Text Search**: Elasticsearch integration for advanced search capabilities
2. **Caching Layer**: Redis caching for frequently accessed announcements
3. **CDN Integration**: Content delivery network for image optimization
4. **Real-time Updates**: WebSocket integration for live announcement updates
5. **API Versioning**: Versioned API endpoints for backward compatibility
6. **Rate Limiting**: API rate limiting to prevent abuse
7. **Audit Logging**: Comprehensive audit trail for all operations
8. **Backup Strategy**: Automated backup and recovery procedures

## Maintenance Guidelines

### Regular Tasks
1. **Database Cleanup**: Periodically review and clean up old announcements
2. **Performance Monitoring**: Monitor API response times and database performance
3. **Security Updates**: Keep dependencies updated for security patches
4. **Backup Verification**: Regularly test backup and restore procedures

### Monitoring
1. **Error Tracking**: Monitor error rates and investigate anomalies
2. **Usage Analytics**: Track announcement creation and viewing patterns
3. **Performance Metrics**: Monitor database query performance
4. **Security Audits**: Regular security assessments and penetration testing

## Conclusion

The Announcement System provides a robust, secure, and user-friendly solution for managing announcements in the L Café application. With comprehensive CRUD operations, security features, and seamless integration with other system components, it serves as a solid foundation for content management needs.

The system follows best practices for web development, including proper error handling, security measures, and performance optimization. The modular architecture makes it easy to extend and maintain, while the comprehensive API documentation ensures smooth integration with frontend components.

Key strengths of the implementation:
- **Security-First Design**: Comprehensive authentication, authorization, and CSRF protection
- **Performance Optimized**: Efficient database queries, pagination, and caching strategies
- **User-Friendly**: Intuitive admin interface and responsive public displays
- **Maintainable**: Clean code structure with proper separation of concerns
- **Extensible**: Modular design allows for easy feature additions
- **SEO-Friendly**: Automatic slug generation and sitemap integration

This documentation serves as a complete reference for developers working with the announcement system, providing both high-level architecture understanding and detailed implementation guidance.
```
