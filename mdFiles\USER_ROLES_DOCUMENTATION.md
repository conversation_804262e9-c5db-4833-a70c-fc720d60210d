# User Roles System Documentation

## Overview

The User Roles system in L Café implements a comprehensive Role-Based Access Control (RBAC) mechanism that governs user permissions, feature access, and data security across the entire application. The system supports three distinct roles with hierarchical permissions and granular access control.

## Role Hierarchy

### 1. Admin Role (Highest Privilege)

**Full System Access:**
- **User Management**: Create, update, delete, and manage all users
- **Content Management**: Full CRUD operations on all content types
- **System Administration**: Access to system settings and configuration
- **Analytics and Monitoring**: Complete access to visitor data and system metrics
- **Security Management**: Manage authentication and authorization settings

**Exclusive Permissions:**
- Create and delete user accounts
- Modify user roles and permissions
- Access system-wide analytics
- Configure application settings
- Manage security policies
- View system logs and monitoring data

### 2. Staff Role (Content Management)

**Content Management Access:**
- **Announcements**: Create, edit, and publish announcements
- **Menu Management**: Manage food and beverage items
- **Gallery Management**: Upload and organize images
- **Q&A Management**: Respond to customer questions
- **Dashboard Access**: View content statistics and metrics

**Limited Permissions:**
- Cannot manage other users
- Cannot access system settings
- Cannot view detailed analytics
- Cannot modify security settings
- Can only edit content they created (with some exceptions)

### 3. Customer Role (Public Access)

**Basic User Access:**
- **Profile Management**: Edit own profile information
- **Public Content**: View announcements, menu, gallery
- **Q&A Interaction**: Submit questions and view answers
- **Account Settings**: Change password and personal details

**Restricted Access:**
- No dashboard access
- Cannot create or edit content
- Cannot view other user information
- Cannot access administrative features

## Implementation Architecture

### 1. Database Schema

**User Model (`backend/models/user.model.js`):**
```javascript
const userSchema = new mongoose.Schema({
  username: { 
    type: String, 
    required: true, 
    unique: true,
    minlength: 3,
    maxlength: 30
  },
  email: { 
    type: String, 
    required: true, 
    unique: true,
    lowercase: true
  },
  password: { 
    type: String, 
    required: true,
    minlength: 6
  },
  role: { 
    type: String, 
    enum: ['admin', 'staff', 'customer'], 
    default: 'customer',
    required: true
  },
  active: { 
    type: Boolean, 
    default: true 
  },
  firstName: { 
    type: String,
    maxlength: 50
  },
  lastName: { 
    type: String,
    maxlength: 50
  },
  lastLogin: { 
    type: Date 
  },
  createdAt: { 
    type: Date, 
    default: Date.now 
  },
  updatedAt: { 
    type: Date, 
    default: Date.now 
  }
});

// Indexes for performance
userSchema.index({ role: 1, active: 1 });
userSchema.index({ email: 1 });
userSchema.index({ username: 1 });
```

### 2. Middleware Implementation

**Authentication Middleware (`backend/middleware/auth.middleware.js`):**
```javascript
export const authMiddleware = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ 
        message: 'Access denied. No token provided.' 
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findById(decoded.id).select('-password');
    
    if (!user) {
      return res.status(401).json({ 
        message: 'Invalid token. User not found.' 
      });
    }

    if (!user.active) {
      return res.status(401).json({ 
        message: 'Account is deactivated.' 
      });
    }

    req.user = user;
    next();
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ 
        message: 'Token expired. Please login again.' 
      });
    }
    
    res.status(401).json({ 
      message: 'Invalid token.' 
    });
  }
};
```

**Admin Authorization Middleware:**
```javascript
export const adminMiddleware = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({ 
      message: 'Authentication required.' 
    });
  }

  if (req.user.role !== 'admin') {
    return res.status(403).json({ 
      message: 'Access denied. Admin privileges required.',
      userRole: req.user.role,
      requiredRole: 'admin'
    });
  }

  next();
};
```

**Staff Authorization Middleware:**
```javascript
export const staffMiddleware = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({ 
      message: 'Authentication required.' 
    });
  }

  if (!['admin', 'staff'].includes(req.user.role)) {
    return res.status(403).json({ 
      message: 'Access denied. Staff privileges required.',
      userRole: req.user.role,
      requiredRole: 'staff'
    });
  }

  next();
};
```

### 3. Frontend Role Management

**Authentication Context (`src/contexts/NewAuthContext.tsx`):**
```typescript
interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isAdmin: boolean;
  isStaff: boolean;
  isCustomer: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  updateUser: (userData: Partial<User>) => void;
}

const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Role-based computed properties
  const isAdmin = user?.role === 'admin';
  const isStaff = user?.role === 'staff' || user?.role === 'admin';
  const isCustomer = user?.role === 'customer';

  const contextValue = {
    user,
    isAuthenticated,
    isAdmin,
    isStaff,
    isCustomer,
    login,
    logout,
    updateUser
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};
```

**Role-Based Route Protection:**
```typescript
// Protected route component
const ProtectedRoute: React.FC<{
  children: React.ReactNode;
  requiredRole?: 'admin' | 'staff' | 'customer';
  fallback?: React.ReactNode;
}> = ({ children, requiredRole, fallback }) => {
  const { user, isAuthenticated } = useAuth();

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  if (requiredRole) {
    const hasPermission = checkRolePermission(user?.role, requiredRole);
    if (!hasPermission) {
      return fallback || <AccessDenied />;
    }
  }

  return <>{children}</>;
};

// Role permission checker
const checkRolePermission = (userRole: string, requiredRole: string): boolean => {
  const roleHierarchy = {
    admin: 3,
    staff: 2,
    customer: 1
  };

  return roleHierarchy[userRole] >= roleHierarchy[requiredRole];
};
```

## Permission Matrix

### Detailed Access Control

| Feature/Action | Admin | Staff | Customer | Notes |
|----------------|-------|-------|----------|-------|
| **Authentication** |
| Login/Logout | ✅ | ✅ | ✅ | All users |
| Change Own Password | ✅ | ✅ | ✅ | All users |
| **Dashboard Access** |
| View Dashboard | ✅ | ✅ | ❌ | Staff+ only |
| View Statistics | ✅ | ✅ | ❌ | Staff+ only |
| **User Management** |
| View All Users | ✅ | ❌ | ❌ | Admin only |
| Create Users | ✅ | ❌ | ❌ | Admin only |
| Edit User Profiles | ✅ | ❌ | ❌ | Admin only |
| Change User Roles | ✅ | ❌ | ❌ | Admin only |
| Deactivate Users | ✅ | ❌ | ❌ | Admin only |
| Delete Users | ✅ | ❌ | ❌ | Admin only |
| Reset User Passwords | ✅ | ❌ | ❌ | Admin only |
| **Content Management** |
| View Announcements | ✅ | ✅ | ✅ | All users (public) |
| Create Announcements | ✅ | ✅ | ❌ | Staff+ only |
| Edit Announcements | ✅ | ✅* | ❌ | *Own content only |
| Delete Announcements | ✅ | ✅* | ❌ | *Own content only |
| Publish/Unpublish | ✅ | ✅ | ❌ | Staff+ only |
| **Menu Management** |
| View Menu | ✅ | ✅ | ✅ | All users (public) |
| Create Menu Items | ✅ | ✅ | ❌ | Staff+ only |
| Edit Menu Items | ✅ | ✅ | ❌ | Staff+ only |
| Delete Menu Items | ✅ | ✅ | ❌ | Staff+ only |
| Manage Categories | ✅ | ✅ | ❌ | Staff+ only |
| **Gallery Management** |
| View Gallery | ✅ | ✅ | ✅ | All users (public) |
| Upload Images | ✅ | ✅ | ❌ | Staff+ only |
| Edit Image Details | ✅ | ✅ | ❌ | Staff+ only |
| Delete Images | ✅ | ✅ | ❌ | Staff+ only |
| Manage Categories | ✅ | ✅ | ❌ | Staff+ only |
| **Q&A Management** |
| View Q&A | ✅ | ✅ | ✅ | All users (public) |
| Submit Questions | ✅ | ✅ | ✅ | All users |
| Answer Questions | ✅ | ✅ | ❌ | Staff+ only |
| Moderate Questions | ✅ | ✅ | ❌ | Staff+ only |
| Delete Q&A Items | ✅ | ✅ | ❌ | Staff+ only |
| **Analytics & Monitoring** |
| View Visitor Analytics | ✅ | ❌ | ❌ | Admin only |
| View System Logs | ✅ | ❌ | ❌ | Admin only |
| Monitor Performance | ✅ | ❌ | ❌ | Admin only |
| **System Settings** |
| Modify App Settings | ✅ | ❌ | ❌ | Admin only |
| Manage Security | ✅ | ❌ | ❌ | Admin only |
| Database Management | ✅ | ❌ | ❌ | Admin only |
| **Profile Management** |
| View Own Profile | ✅ | ✅ | ✅ | All users |
| Edit Own Profile | ✅ | ✅ | ✅ | All users |
| View Other Profiles | ✅ | ❌ | ❌ | Admin only |

## Role Management Operations

### 1. User Creation with Roles

**Backend Controller (`backend/controllers/user.controller.js`):**
```javascript
export const createUser = async (req, res) => {
  try {
    const { username, email, password, role, firstName, lastName } = req.body;

    // Validate role
    const validRoles = ['admin', 'staff', 'customer'];
    if (role && !validRoles.includes(role)) {
      return res.status(400).json({ 
        message: 'Invalid role specified',
        validRoles 
      });
    }

    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [{ email }, { username }]
    });

    if (existingUser) {
      return res.status(400).json({ 
        message: 'User already exists with this email or username' 
      });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create user
    const user = new User({
      username,
      email,
      password: hashedPassword,
      role: role || 'customer',
      firstName,
      lastName,
      active: true
    });

    await user.save();

    // Return user without password
    const userResponse = user.toObject();
    delete userResponse.password;

    res.status(201).json({
      message: 'User created successfully',
      user: userResponse
    });
  } catch (error) {
    res.status(500).json({ 
      message: 'Error creating user', 
      error: error.message 
    });
  }
};
```

### 2. Role Update Operations

**Update User Role:**
```javascript
export const updateUserRole = async (req, res) => {
  try {
    const { id } = req.params;
    const { role } = req.body;

    // Validate role
    const validRoles = ['admin', 'staff', 'customer'];
    if (!validRoles.includes(role)) {
      return res.status(400).json({ 
        message: 'Invalid role specified',
        validRoles 
      });
    }

    // Prevent self-demotion for admins
    if (req.user.id === id && req.user.role === 'admin' && role !== 'admin') {
      return res.status(400).json({ 
        message: 'Cannot demote yourself from admin role' 
      });
    }

    const user = await User.findByIdAndUpdate(
      id,
      { role, updatedAt: Date.now() },
      { new: true }
    ).select('-password');

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.status(200).json({
      message: 'User role updated successfully',
      user
    });
  } catch (error) {
    res.status(500).json({ 
      message: 'Error updating user role', 
      error: error.message 
    });
  }
};
```

### 3. Frontend Role Management

**User Management Component:**
```typescript
const UserManagement: React.FC = () => {
  const { user: currentUser } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  const handleRoleChange = async (userId: string, newRole: string) => {
    try {
      await userService.updateUserRole(userId, newRole);
      
      // Update local state
      setUsers(users.map(user => 
        user._id === userId 
          ? { ...user, role: newRole }
          : user
      ));

      toast.success('User role updated successfully');
    } catch (error) {
      toast.error('Failed to update user role');
    }
  };

  const RoleSelector: React.FC<{ user: User }> = ({ user }) => (
    <Select
      value={user.role}
      onValueChange={(newRole) => handleRoleChange(user._id, newRole)}
      disabled={user._id === currentUser?.id} // Prevent self-modification
    >
      <SelectTrigger>
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="customer">Customer</SelectItem>
        <SelectItem value="staff">Staff</SelectItem>
        <SelectItem value="admin">Admin</SelectItem>
      </SelectContent>
    </Select>
  );

  return (
    <div className="user-management">
      {/* User list with role management */}
    </div>
  );
};
```

## Security Considerations

### 1. Role Validation

**Server-Side Validation:**
- All role changes validated on backend
- Middleware enforces role requirements
- Database constraints prevent invalid roles
- Audit logging for role changes

**Client-Side Protection:**
- UI elements hidden based on roles
- Route protection prevents unauthorized access
- API calls include role validation
- Real-time permission checking

### 2. Privilege Escalation Prevention

**Security Measures:**
- Users cannot modify their own roles
- Admin role required for user management
- Role changes require CSRF protection
- Audit trail for all role modifications

**Implementation:**
```javascript
// Prevent self-role modification
if (req.user.id === targetUserId && req.body.role !== req.user.role) {
  return res.status(403).json({ 
    message: 'Cannot modify your own role' 
  });
}

// Ensure only admins can create other admins
if (req.body.role === 'admin' && req.user.role !== 'admin') {
  return res.status(403).json({ 
    message: 'Only admins can create admin users' 
  });
}
```

### 3. Session Management

**Role-Based Sessions:**
- JWT tokens include role information
- Token validation checks role consistency
- Role changes invalidate existing tokens
- Session timeout based on role sensitivity

## Testing Role-Based Access

### 1. Unit Tests

**Role Middleware Tests:**
```javascript
describe('Role Middleware', () => {
  it('should allow admin access to admin routes', async () => {
    const req = { user: { role: 'admin' } };
    const res = {};
    const next = jest.fn();

    adminMiddleware(req, res, next);
    expect(next).toHaveBeenCalled();
  });

  it('should deny staff access to admin routes', async () => {
    const req = { user: { role: 'staff' } };
    const res = { 
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };
    const next = jest.fn();

    adminMiddleware(req, res, next);
    expect(res.status).toHaveBeenCalledWith(403);
    expect(next).not.toHaveBeenCalled();
  });
});
```

### 2. Integration Tests

**Role-Based API Tests:**
```javascript
describe('User Management API', () => {
  it('should allow admin to create users', async () => {
    const response = await request(app)
      .post('/api/users')
      .set('Authorization', `Bearer ${adminToken}`)
      .send({
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
        role: 'staff'
      })
      .expect(201);

    expect(response.body.user.role).toBe('staff');
  });

  it('should deny staff from creating users', async () => {
    await request(app)
      .post('/api/users')
      .set('Authorization', `Bearer ${staffToken}`)
      .send({
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123'
      })
      .expect(403);
  });
});
```

## Best Practices

### 1. Role Design Principles

**Principle of Least Privilege:**
- Grant minimum necessary permissions
- Regular permission audits
- Role-based feature access
- Granular permission control

**Role Hierarchy:**
- Clear role inheritance
- Logical permission escalation
- Consistent role naming
- Well-defined boundaries

### 2. Implementation Guidelines

**Backend Security:**
- Always validate roles server-side
- Use middleware for consistent enforcement
- Implement audit logging
- Regular security reviews

**Frontend UX:**
- Hide unavailable features
- Provide clear access feedback
- Graceful permission errors
- Role-appropriate interfaces

### 3. Maintenance and Monitoring

**Regular Audits:**
- Review user roles quarterly
- Monitor privilege escalation attempts
- Audit role change logs
- Validate permission consistency

**Performance Monitoring:**
- Track role-based query performance
- Monitor authentication overhead
- Optimize role checking logic
- Cache role permissions appropriately

## Conclusion

The User Roles system in L Café provides a robust, secure, and scalable foundation for access control and user management. The implementation follows security best practices while maintaining usability and performance across all user types.

Key strengths:
- **Comprehensive RBAC**: Full role-based access control
- **Security-First Design**: Multiple layers of protection
- **Scalable Architecture**: Easy to extend and modify
- **User-Friendly Interface**: Intuitive role management
- **Performance Optimized**: Efficient permission checking
- **Audit-Ready**: Complete logging and monitoring
