# Admin Panel Documentation

## Overview

The Admin Panel is a comprehensive administrative interface for L Café that provides role-based access control and management capabilities for all aspects of the application. It features a modern, responsive design with secure authentication, real-time data management, and intuitive user experience.

## Architecture

### Frontend Implementation

#### 1. Dashboard Layout (`src/pages/Dashboard.tsx`)

The main dashboard component provides a unified interface with:

**Core Features:**
- **Responsive Sidebar Navigation**: Collapsible sidebar with role-based menu items
- **Dynamic Content Area**: Route-based content rendering
- **Authentication Guard**: Automatic redirect for unauthenticated users
- **Role-Based Access**: Different features based on user roles
- **Real-time Updates**: Live data synchronization

**Component Structure:**
```typescript
const Dashboard = () => {
  const { user, isAuthenticated } = useAuth();
  const [activeTab, setActiveTab] = useState("overview");
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const location = useLocation();

  // Authentication guard
  if (!isAuthenticated || !user) {
    return null;
  }

  // Dynamic tab management
  useEffect(() => {
    const path = location.pathname.split("/")[2] || "overview";
    setActiveTab(path);
  }, [location.pathname]);

  return (
    <div className="flex h-screen bg-gray-100">
      <Sidebar />
      <MainContent />
    </div>
  );
};
```

#### 2. Sidebar Navigation (`src/components/dashboard/Sidebar.tsx`)

**Navigation Structure:**
- **Overview**: Dashboard home with statistics
- **Announcements**: Content management
- **Menu**: Food and beverage management
- **Gallery**: Image management
- **Q&A**: FAQ management
- **Users**: User management (Admin only)
- **Visitors**: Analytics and tracking
- **Settings**: System configuration

**Role-Based Menu Items:**
```typescript
const menuItems = [
  { id: "overview", label: "Overview", icon: Home, path: "/dashboard" },
  { id: "announcements", label: "Announcements", icon: Megaphone, path: "/dashboard/announcements" },
  { id: "menu", label: "Menu", icon: Coffee, path: "/dashboard/menu" },
  { id: "gallery", label: "Gallery", icon: Image, path: "/dashboard/gallery" },
  { id: "qa", label: "Q&A", icon: HelpCircle, path: "/dashboard/qa" },
  ...(user?.role === 'admin' ? [
    { id: "users", label: "Users", icon: Users, path: "/dashboard/users" },
    { id: "visitors", label: "Visitors", icon: BarChart3, path: "/dashboard/visitors" },
    { id: "settings", label: "Settings", icon: Settings, path: "/dashboard/settings" }
  ] : [])
];
```

#### 3. Dashboard Components

**Overview Dashboard (`src/components/dashboard/DashboardOverview.tsx`)**
- **Statistics Cards**: Key metrics and counts
- **Recent Activity**: Latest content updates
- **Quick Actions**: Common administrative tasks
- **System Status**: Health indicators

**Content Management Components:**
- **DashboardAnnouncements**: News and updates management
- **DashboardMenu**: Food and beverage catalog
- **DashboardGallery**: Image and media management
- **DashboardQA**: FAQ and help content

**Admin-Only Components:**
- **DashboardUsers**: User account management
- **DashboardVisitors**: Analytics and visitor tracking
- **DashboardSettings**: System configuration

### Backend Implementation

#### 1. Authentication Middleware (`backend/middleware/auth.middleware.js`)

**JWT Token Validation:**
```javascript
export const authMiddleware = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ message: 'Access denied. No token provided.' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findById(decoded.id).select('-password');
    
    if (!user || !user.active) {
      return res.status(401).json({ message: 'Invalid token or inactive user.' });
    }

    req.user = user;
    next();
  } catch (error) {
    res.status(401).json({ message: 'Invalid token.' });
  }
};
```

#### 2. Admin Authorization (`backend/middleware/admin.middleware.js`)

**Role-Based Access Control:**
```javascript
export const adminMiddleware = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({ message: 'Authentication required.' });
  }

  if (req.user.role !== 'admin') {
    return res.status(403).json({ 
      message: 'Access denied. Admin privileges required.' 
    });
  }

  next();
};
```

#### 3. User Management Routes (`backend/routes/user.routes.js`)

**Protected Admin Routes:**
```javascript
// Get all users (admin only)
router.get('/', [authMiddleware, adminMiddleware], getAllUsers);

// Update user role (admin only)
router.put('/:id/role', [authMiddleware, adminMiddleware, csrfProtection], updateUserRole);

// Toggle user status (admin only)
router.put('/:id/toggle-status', [authMiddleware, adminMiddleware, csrfProtection], toggleUserStatus);

// Change user password (admin only)
router.put('/:id/change-password', [authMiddleware, adminMiddleware, csrfProtection], changeUserPassword);

// Delete user (admin only)
router.delete('/:id', [authMiddleware, adminMiddleware, csrfProtection], deleteUser);
```

## User Roles System

### 1. Role Definitions

**Admin Role:**
- **Full Access**: Complete system administration
- **User Management**: Create, update, delete users
- **Content Management**: All content types
- **System Settings**: Configuration and maintenance
- **Analytics**: Visitor tracking and statistics

**Staff Role:**
- **Content Management**: Announcements, menu, gallery, Q&A
- **Limited User Access**: View own profile only
- **No System Settings**: Cannot modify system configuration
- **No User Management**: Cannot manage other users

**Customer Role:**
- **Public Access**: View public content only
- **Profile Management**: Manage own profile
- **No Admin Access**: Cannot access dashboard
- **Read-Only**: Cannot modify content

### 2. Role Implementation

**Database Schema (`backend/models/user.model.js`):**
```javascript
const userSchema = new mongoose.Schema({
  username: { type: String, required: true, unique: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  role: { 
    type: String, 
    enum: ['admin', 'staff', 'customer'], 
    default: 'customer' 
  },
  active: { type: Boolean, default: true },
  firstName: { type: String },
  lastName: { type: String },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});
```

**Frontend Role Checking:**
```typescript
// Authentication context
const useAuth = () => {
  const context = useContext(AuthContext);
  
  return {
    ...context,
    isAdmin: context.user?.role === 'admin',
    isStaff: context.user?.role === 'staff' || context.user?.role === 'admin',
    isCustomer: context.user?.role === 'customer'
  };
};
```

### 3. Permission Matrix

| Feature | Admin | Staff | Customer |
|---------|-------|-------|----------|
| Dashboard Access | ✅ | ✅ | ❌ |
| View Statistics | ✅ | ✅ | ❌ |
| Manage Announcements | ✅ | ✅ | ❌ |
| Manage Menu | ✅ | ✅ | ❌ |
| Manage Gallery | ✅ | ✅ | ❌ |
| Manage Q&A | ✅ | ✅ | ❌ |
| User Management | ✅ | ❌ | ❌ |
| View Analytics | ✅ | ❌ | ❌ |
| System Settings | ✅ | ❌ | ❌ |
| Delete Content | ✅ | ✅* | ❌ |
| Create Users | ✅ | ❌ | ❌ |

*Staff can delete content they created

## Dashboard Features

### 1. Overview Dashboard

**Statistics Display:**
- **Total Users**: Active user count
- **Total Announcements**: Published announcements
- **Total Menu Items**: Active menu items
- **Total Gallery Items**: Published images
- **Recent Activity**: Latest content updates

**Quick Actions:**
- **Add Announcement**: Quick content creation
- **Add Menu Item**: Fast menu updates
- **Upload Image**: Gallery management
- **View Analytics**: Access visitor data

### 2. Content Management

**Announcements Management:**
- **CRUD Operations**: Create, read, update, delete
- **Rich Text Editor**: Formatted content creation
- **Category Management**: Organize by type
- **Publication Control**: Draft/published states
- **SEO Optimization**: Meta tags and slugs

**Menu Management:**
- **Item Categories**: Coffee, food, drinks, etc.
- **Pricing Management**: Dynamic price updates
- **Availability Control**: Enable/disable items
- **Image Association**: Link gallery images
- **Nutritional Information**: Dietary details

**Gallery Management:**
- **Image Upload**: Drag-and-drop interface
- **Thumbnail Generation**: Automatic optimization
- **Category Organization**: Systematic filing
- **Bulk Operations**: Multiple image handling
- **Featured Images**: Highlight special content

**Q&A Management:**
- **Question Moderation**: Approve/reject questions
- **Answer Creation**: Staff responses
- **Category Organization**: Topic-based grouping
- **Search Functionality**: Quick question finding
- **Public/Private Control**: Visibility settings

### 3. User Management (Admin Only)

**User Operations:**
```typescript
// User management interface
interface UserManagement {
  viewUsers: () => Promise<User[]>;
  createUser: (userData: CreateUserData) => Promise<User>;
  updateUser: (id: string, userData: UpdateUserData) => Promise<User>;
  updateUserRole: (id: string, role: UserRole) => Promise<User>;
  toggleUserStatus: (id: string) => Promise<User>;
  changeUserPassword: (id: string, newPassword: string) => Promise<void>;
  deleteUser: (id: string) => Promise<void>;
}
```

**User List Features:**
- **Pagination**: Handle large user lists
- **Search and Filter**: Find users quickly
- **Role Indicators**: Visual role identification
- **Status Badges**: Active/inactive states
- **Quick Actions**: Common operations
- **Bulk Operations**: Multiple user management

**User Creation:**
- **Form Validation**: Comprehensive input checking
- **Role Assignment**: Admin-controlled role setting
- **Password Generation**: Secure default passwords
- **Email Verification**: Optional verification process
- **Profile Setup**: Basic information collection

### 4. Analytics and Monitoring

**Visitor Tracking:**
- **Page Views**: Track popular content
- **User Sessions**: Monitor engagement
- **Geographic Data**: Visitor locations
- **Device Information**: Platform analytics
- **Time-based Analysis**: Usage patterns

**System Monitoring:**
- **Performance Metrics**: Response times
- **Error Tracking**: System issues
- **Database Health**: Connection status
- **Storage Usage**: File system monitoring
- **Security Events**: Authentication logs

## Security Features

### 1. Authentication Security

**JWT Implementation:**
- **Secure Tokens**: Cryptographically signed
- **Expiration Control**: Time-limited access
- **Refresh Mechanism**: Seamless token renewal
- **Secure Storage**: HTTP-only cookies option
- **Cross-Site Protection**: CSRF token integration

**Password Security:**
- **Bcrypt Hashing**: Industry-standard encryption
- **Salt Rounds**: Configurable security level
- **Password Policies**: Strength requirements
- **Change Tracking**: Password history
- **Reset Functionality**: Secure recovery process

### 2. Authorization Security

**Role-Based Access Control (RBAC):**
- **Principle of Least Privilege**: Minimal necessary access
- **Route Protection**: Middleware-based security
- **Component-Level Security**: Frontend access control
- **API Endpoint Security**: Backend route protection
- **Dynamic Permissions**: Context-aware access

**Session Management:**
- **Session Tracking**: Active session monitoring
- **Concurrent Sessions**: Multiple device support
- **Session Invalidation**: Logout and timeout
- **Security Events**: Login attempt tracking
- **Anomaly Detection**: Unusual activity alerts

### 3. Data Protection

**Input Validation:**
- **Schema Validation**: Mongoose schema enforcement
- **Sanitization**: XSS prevention
- **Type Checking**: Data type validation
- **Length Limits**: Prevent overflow attacks
- **Format Validation**: Email, phone, etc.

**CSRF Protection:**
- **Token Validation**: Request authenticity
- **Double Submit Cookies**: Enhanced security
- **SameSite Cookies**: Cross-site protection
- **Origin Validation**: Request source verification
- **Automatic Integration**: Seamless protection

## API Reference

### Authentication Endpoints

```http
POST /api/auth/login
Content-Type: application/json
X-CSRF-Token: <token>

{
  "username": "admin",
  "password": "password"
}
```

**Response:**
```json
{
  "token": "jwt-token",
  "user": {
    "id": "user-id",
    "username": "admin",
    "email": "<EMAIL>",
    "role": "admin",
    "firstName": "Admin",
    "lastName": "User"
  }
}
```

### User Management Endpoints

**Get All Users (Admin Only):**
```http
GET /api/users?page=1&limit=10&search=username&role=admin
Authorization: Bearer <token>
```

**Update User Role (Admin Only):**
```http
PUT /api/users/:id/role
Authorization: Bearer <token>
X-CSRF-Token: <token>
Content-Type: application/json

{
  "role": "staff"
}
```

**Toggle User Status (Admin Only):**
```http
PUT /api/users/:id/toggle-status
Authorization: Bearer <token>
X-CSRF-Token: <token>
```

**Change User Password (Admin Only):**
```http
PUT /api/users/:id/change-password
Authorization: Bearer <token>
X-CSRF-Token: <token>
Content-Type: application/json

{
  "newPassword": "newSecurePassword"
}
```

## Configuration

### Environment Variables

```env
# Authentication
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=24h

# Admin User (for initial setup)
ADMIN_USERNAME=admin
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret

# CORS
CORS_ORIGIN=http://localhost:8080
```

### Initial Admin Setup

```javascript
// Create initial admin user
const createAdminUser = async () => {
  const adminExists = await User.findOne({ role: 'admin' });
  
  if (!adminExists) {
    const hashedPassword = await bcrypt.hash(process.env.ADMIN_PASSWORD, 12);
    
    const admin = new User({
      username: process.env.ADMIN_USERNAME,
      email: process.env.ADMIN_EMAIL,
      password: hashedPassword,
      role: 'admin',
      firstName: 'System',
      lastName: 'Administrator',
      active: true
    });
    
    await admin.save();
    console.log('Admin user created successfully');
  }
};
```

## Usage Guide

### 1. Accessing the Admin Panel

1. **Navigate to Login**: `http://localhost:8080/login`
2. **Enter Credentials**: Use admin username and password
3. **Access Dashboard**: Automatic redirect to `/dashboard`
4. **Navigate Features**: Use sidebar navigation

### 2. User Management Workflow

**Creating a New User:**
1. Navigate to Dashboard > Users
2. Click "Add User" button
3. Fill in user details (username, email, role)
4. Set initial password
5. Save and notify user

**Managing User Roles:**
1. Find user in user list
2. Click role dropdown
3. Select new role (admin/staff/customer)
4. Confirm change
5. User permissions update immediately

**Handling User Issues:**
1. **Account Lockout**: Toggle user status
2. **Password Reset**: Use change password feature
3. **Role Changes**: Update role as needed
4. **Account Deletion**: Permanent user removal

### 3. Content Management Workflow

**Publishing Announcements:**
1. Navigate to Dashboard > Announcements
2. Click "Add Announcement"
3. Enter title, content, and category
4. Set publication status
5. Save and publish

**Menu Updates:**
1. Navigate to Dashboard > Menu
2. Select category or create new item
3. Update pricing and availability
4. Associate images if needed
5. Save changes

**Gallery Management:**
1. Navigate to Dashboard > Gallery
2. Upload new images
3. Organize by categories
4. Set featured images
5. Manage visibility

## Troubleshooting

### Common Issues

#### 1. Access Denied Errors
**Symptoms**: 403 Forbidden on admin routes
**Causes**: 
- Insufficient user role
- Expired authentication token
- Missing CSRF token

**Solutions:**
- Verify user role in database
- Re-login to refresh token
- Clear browser cache and cookies

#### 2. Navigation Issues
**Symptoms**: Sidebar links not working
**Causes**:
- JavaScript errors
- Route configuration issues
- Authentication state problems

**Solutions:**
- Check browser console for errors
- Verify route definitions
- Confirm authentication status

#### 3. User Management Problems
**Symptoms**: Cannot create or modify users
**Causes**:
- Database connection issues
- Validation errors
- Permission problems

**Solutions:**
- Check database connectivity
- Verify input validation
- Confirm admin privileges

### Debug Commands

```bash
# Check user roles in database
mongo l-cafe --eval "db.users.find({}, {username:1, role:1, active:1}).pretty()"

# Verify admin user exists
mongo l-cafe --eval "db.users.findOne({role: 'admin'})"

# Check authentication middleware
curl -H "Authorization: Bearer TOKEN" http://localhost:5000/api/users

# Test CSRF protection
curl -H "X-CSRF-Token: TOKEN" -H "Authorization: Bearer TOKEN" \
     -X POST http://localhost:5000/api/users
```

## Performance Considerations

### 1. Frontend Optimization

**Code Splitting:**
- Lazy load dashboard components
- Route-based code splitting
- Component-level optimization

**State Management:**
- Efficient React Query usage
- Minimal re-renders
- Optimized data fetching

**UI Performance:**
- Virtual scrolling for large lists
- Debounced search inputs
- Optimized image loading

### 2. Backend Optimization

**Database Queries:**
- Indexed user lookups
- Paginated results
- Efficient aggregations

**Caching Strategy:**
- User session caching
- Role-based caching
- API response caching

**Security Performance:**
- Optimized JWT verification
- Efficient password hashing
- Minimal middleware overhead

## Conclusion

The Admin Panel and User Roles system provides a comprehensive, secure, and user-friendly administrative interface for L Café. The implementation follows industry best practices for security, performance, and usability while maintaining flexibility for future enhancements.

Key strengths:
- **Comprehensive Security**: Multi-layered protection with RBAC
- **Intuitive Interface**: Modern, responsive design
- **Scalable Architecture**: Modular, maintainable codebase
- **Role-Based Access**: Flexible permission system
- **Real-time Updates**: Live data synchronization
- **Performance Optimized**: Efficient queries and caching
